import Analyzer from '@next/bundle-analyzer';
import type {NextConfig} from 'next';

const withBundleAnalyzer = Analyzer({
  enabled: process.env.ANALYZE === 'true',
})


const nextConfig: NextConfig = withBundleAnalyzer({
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: false,
    formats: ['image/webp', 'image/avif'],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: 'default-src \'self\'; script-src \'none\'; sandbox;',
  },
  compiler: {
    // Remove React properties
    reactRemoveProperties: true,
    // Remove console.log in production builds
    removeConsole: process.env.NODE_ENV === 'production',
  },
  experimental: {
    // reactCompiler: true,
    // taint: true,
    optimizePackageImports: ['lucide-react', 'date-fns'],
  },
});

export default nextConfig;
